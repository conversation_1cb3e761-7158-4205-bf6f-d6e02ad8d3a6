'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Github, Linkedin, Facebook, Instagram } from 'lucide-react'
import { useAuth } from '@/components/providers/AuthProvider'

const ADMIN_UID = 'KNlrg408xubJeEmwFpUbeDQWBgF3'

export default function Footer() {
  const currentYear = new Date().getFullYear()
  const { user } = useAuth()

  const socialLinks = [
    {
      name: 'GitHub',
      href: 'https://github.com/ernstrmlo',
      icon: Github
    },
    {
      name: 'LinkedIn',
      href: 'https://linkedin.com/in/ernstrmlo',
      icon: Linkedin
    },
    {
      name: 'Facebook',
      href: 'https://facebook.com/ernstrmlo',
      icon: Facebook
    },
    {
      name: 'Instagram',
      href: 'https://instagram.com/ernstrmlo',
      icon: Instagram
    }
  ]

  // Use admin's profile picture if available, otherwise fallback to static avatar
  const avatarSrc = user?.uid === ADMIN_UID && user?.photoURL
    ? user.photoURL
    : "/images/avatar.jpg"

  return (
    <footer className="bg-transparent mt-20 relative z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col items-center space-y-8">
          {/* Avatar */}
          <div className="relative w-16 h-16 rounded-full overflow-hidden ring-2 ring-primary-200 dark:ring-primary-600">
            <Image
              src={avatarSrc}
              alt="Ernst Romelo"
              width={64}
              height={64}
              className="object-cover rounded-full"
              priority
            />
          </div>

          {/* Tagline */}
          <div className="text-center max-w-2xl">
            <p className="text-lg text-gray-900 dark:text-gray-100 font-medium mb-2">
              Ernst Romelo
            </p>
            <p className="text-gray-700 dark:text-gray-200 leading-relaxed">
              Crafting intelligent solutions through AI automation, modern web development,
              and innovative app experiences that transform ideas into digital reality.
            </p>
          </div>

          {/* Social Media Links */}
          <div className="flex items-center justify-center space-x-6">
            {socialLinks.map((social) => {
              const IconComponent = social.icon
              return (
                <Link
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="no-link-style text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300"
                  aria-label={social.name}
                >
                  <IconComponent className="w-6 h-6" />
                </Link>
              )
            })}
          </div>

          {/* Copyright */}
          <div className="text-center pt-8 border-t border-neutral-200 dark:border-neutral-700 w-full">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              © 2021 - {currentYear} Ernst Romelo. All rights reserved.
              {' • '}
              <Link
                href="/privacy-policy"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline underline-offset-2 transition-colors duration-200"
              >
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
